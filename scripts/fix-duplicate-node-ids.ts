#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to fix duplicate node IDs in FlowRoom data
 * This script will:
 * 1. Find all FlowRooms with duplicate node IDs
 * 2. Generate unique IDs for duplicate nodes
 * 3. Update the database with the fixed data
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

interface FlowNode {
  id: string
  type: string
  position: { x: number; y: number }
  data: any
  [key: string]: any
}

interface FlowData {
  nodes: FlowNode[]
  edges: any[]
  [key: string]: any
}

function generateUniqueNodeId(existingIds: Set<string>): string {
  const timestamp = Date.now()
  const randomSuffix = Math.random().toString(36).substring(2, 8)
  let newId = `node-${timestamp}-${randomSuffix}`
  
  // Ensure uniqueness (very unlikely to collide, but just in case)
  while (existingIds.has(newId)) {
    const newRandomSuffix = Math.random().toString(36).substring(2, 8)
    newId = `node-${timestamp}-${newRandomSuffix}`
  }
  
  return newId
}

function fixDuplicateNodeIds(flowData: FlowData): { fixed: boolean; data: FlowData } {
  if (!flowData.nodes || !Array.isArray(flowData.nodes)) {
    return { fixed: false, data: flowData }
  }

  const seenIds = new Set<string>()
  const allIds = new Set<string>()
  let hasChanges = false
  
  // First pass: collect all IDs to detect duplicates
  flowData.nodes.forEach(node => {
    if (allIds.has(node.id)) {
      hasChanges = true
    }
    allIds.add(node.id)
  })

  if (!hasChanges) {
    return { fixed: false, data: flowData }
  }

  // Second pass: fix duplicates
  const fixedNodes = flowData.nodes.map(node => {
    if (seenIds.has(node.id)) {
      // This is a duplicate, generate a new unique ID
      const newId = generateUniqueNodeId(seenIds)
      seenIds.add(newId)
      console.log(`  - Fixed duplicate node ID: ${node.id} -> ${newId}`)
      return { ...node, id: newId }
    } else {
      seenIds.add(node.id)
      return node
    }
  })

  // Also fix any edges that reference the old node IDs
  const nodeIdMap = new Map<string, string>()
  flowData.nodes.forEach((originalNode, index) => {
    const fixedNode = fixedNodes[index]
    if (originalNode.id !== fixedNode.id) {
      nodeIdMap.set(originalNode.id, fixedNode.id)
    }
  })

  const fixedEdges = flowData.edges?.map(edge => {
    let updatedEdge = { ...edge }
    if (nodeIdMap.has(edge.source)) {
      updatedEdge.source = nodeIdMap.get(edge.source)
      console.log(`  - Updated edge source: ${edge.source} -> ${updatedEdge.source}`)
    }
    if (nodeIdMap.has(edge.target)) {
      updatedEdge.target = nodeIdMap.get(edge.target)
      console.log(`  - Updated edge target: ${edge.target} -> ${updatedEdge.target}`)
    }
    return updatedEdge
  }) || []

  return {
    fixed: true,
    data: {
      ...flowData,
      nodes: fixedNodes,
      edges: fixedEdges
    }
  }
}

async function main() {
  console.log('🔍 Scanning for FlowRooms with duplicate node IDs...')
  
  try {
    // Get all FlowRooms
    const rooms = await prisma.flowRoom.findMany({
      select: {
        id: true,
        name: true,
        flowData: true
      }
    })

    console.log(`Found ${rooms.length} FlowRooms to check`)

    let totalFixed = 0

    for (const room of rooms) {
      console.log(`\n📋 Checking room: "${room.name}" (${room.id})`)
      
      const flowData = room.flowData as FlowData
      const result = fixDuplicateNodeIds(flowData)
      
      if (result.fixed) {
        console.log(`  ✅ Fixed duplicate node IDs in room "${room.name}"`)
        
        // Update the database
        await prisma.flowRoom.update({
          where: { id: room.id },
          data: { flowData: result.data as any }
        })
        
        totalFixed++
      } else {
        console.log(`  ✨ No duplicate node IDs found in room "${room.name}"`)
      }
    }

    console.log(`\n🎉 Completed! Fixed ${totalFixed} FlowRooms with duplicate node IDs.`)
    
  } catch (error) {
    console.error('❌ Error fixing duplicate node IDs:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
main().catch((error) => {
  console.error('❌ Script failed:', error)
  process.exit(1)
})
